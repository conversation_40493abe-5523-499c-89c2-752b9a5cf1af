# 代理商账户管理页面国际化修改说明

## 修改概述

本次修改为代理商管理模块中的 `AgentAccount.vue` 页面和相关组件添加了中英文翻译支持，参考了现有的代理商管理页面的国际化实现方式。

## 修改的文件

### 1. 国际化配置文件

#### `src/i18n/config/zh.js`
在 `agent` 模块中新增了以下翻译键：

```javascript
// 代理商账户管理页面
accountTitle: '账户',
back: '返回',
remainingAuth: '剩余授权数',
totalPurchased: '总购买数',
operationTime: '操作时间',
eventType: '事件类型',
endCustomer: '终端客户',
deviceSN: '设备SN',
viewMore: '查看更多',
authorizationRecord: '授权记录',
authorization: '授权',
revokeAuthorization: '撤销授权',
startDate: '开始日期',
endDate: '结束日期',
deviceModel: '机型',
allOptions: '全部',
export: '导出',
newAddedCount: '新添加数量',
operator: '操作人',
system: '系统',
exportInProgress: '导出功能开发中...',
// 状态相关
statusNormal: '正常',
statusPending: '待审核',
statusRejected: '审核不通过',
statusDisabled: '禁用',
statusUnknown: '未知状态',
// 功能即将开放
comingSoon: '功能即将开放',
designServiceComingSoon: '设计服务功能即将开放',
aiSoftwareComingSoon: 'AI软件功能即将开放'
```

#### `src/i18n/config/en.js`
在 `agent` 模块中新增了对应的英文翻译：

```javascript
// Agent Account Management Page
accountTitle: "Account",
back: "Back",
remainingAuth: "Remaining Authorizations",
totalPurchased: "Total Purchased",
operationTime: "Operation Time",
eventType: "Event Type",
endCustomer: "End Customer",
deviceSN: "Device SN",
viewMore: "View More",
authorizationRecord: "Authorization Record",
authorization: "Authorization",
revokeAuthorization: "Revoke Authorization",
startDate: "Start Date",
endDate: "End Date",
deviceModel: "Device Model",
allOptions: "All",
export: "Export",
newAddedCount: "New Added Count",
operator: "Operator",
system: "System",
exportInProgress: "Export function under development...",
// Status related
statusNormal: "Normal",
statusPending: "Pending Review",
statusRejected: "Review Failed",
statusDisabled: "Disabled",
statusUnknown: "Unknown Status",
// Coming soon features
comingSoon: "Coming Soon",
designServiceComingSoon: "Design Service feature coming soon",
aiSoftwareComingSoon: "AI Software feature coming soon"
```

### 2. Vue 组件文件

#### `src/views/AgentManagement/AgentAccount.vue`
将硬编码的中文文本替换为 `$t()` 调用：

- 头部导航栏：`返回` → `$t('agent.back')`
- 页面标题：`账户` → `$t('agent.accountTitle')`
- 统计卡片：`剩余授权数`、`总购买数` → `$t('agent.remainingAuth')`、`$t('agent.totalPurchased')`
- 表格表头：`操作时间`、`事件类型`、`终端客户`、`设备SN` → 对应的 `$t()` 调用
- 查看更多按钮：`查看更多` → `$t('agent.viewMore')`
- 状态映射：将状态文本映射改为使用 `$t()` 调用
- 错误提示：使用现有的 `$t('agent.missingOrgCode')`

#### `src/views/AgentManagement/AgentAuthorizationRecord.vue`
将抽屉组件中的硬编码文本替换为 `$t()` 调用：

- 抽屉标题：`授权记录` → `$t('agent.authorizationRecord')`
- 筛选标签：`开始日期`、`结束日期`、`机型`、`事件类型` → 对应的 `$t()` 调用
- 下拉选项：`全部`、`授权`、`撤销授权` → 对应的 `$t()` 调用
- 表格表头：所有列标题都改为使用 `$t()` 调用
- 设备类型映射：`设计服务`、`AI软件` → `$t('agent.designService')`、`$t('agent.aiSoftware')`
- 模拟数据：事件类型和操作人字段使用翻译
- 导出按钮和提示信息

## 使用方式

修改完成后，页面会根据当前语言设置自动显示中文或英文：

1. 中文环境下显示原有的中文文本
2. 英文环境下显示对应的英文翻译
3. 语言切换通过现有的语言切换功能实现

## 测试建议

1. 启动项目：`npm run serve`
2. 进入代理商管理页面
3. 点击"管理账户"按钮打开 AgentAccount 页面
4. 切换语言设置，验证文本是否正确切换
5. 点击"查看更多"打开授权记录抽屉，验证抽屉中的翻译

## 注意事项

1. 所有新增的翻译键都放在现有的 `agent` 模块下，保持了代码结构的一致性
2. 复用了现有的翻译键（如 `agent.back`、`agent.missingOrgCode` 等）
3. 保持了与现有代理商管理页面相同的国际化实现方式
4. 注释掉的功能（设计服务、AI软件选项卡）也添加了翻译支持，便于后续开启
